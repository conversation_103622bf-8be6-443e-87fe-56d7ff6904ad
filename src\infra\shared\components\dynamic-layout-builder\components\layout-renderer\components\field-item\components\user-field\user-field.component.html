@if (shouldShow<PERSON>ield()) {
  <div class="user-field-container" [class.read-only]="isReadOnly()">
    
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <div class="field-value-container">
      
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="field-type-icon me-2">person</mat-icon>
          <div class="user-info">
            <span class="user-name">{{ mockValue() }}</span>
            <small class="user-email text-muted">{{ getUserEmail() }}</small>
          </div>
        </div>
      }
      
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <mat-icon matIconPrefix class="field-type-icon">person</mat-icon>
          
          <mat-select
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            (selectionChange)="onUserChange($event)">
            
            @for (user of mockUsers(); track user.id) {
              <mat-option [value]="user.id">
                <div class="user-option">
                  <span class="user-name">{{ user.name }}</span>
                  <small class="user-email">{{ user.email }}</small>
                </div>
              </mat-option>
            }
          </mat-select>
          
          @if (formControl().invalid && formControl().touched) {
            <mat-error>
              @if (formControl().hasError('required')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED' | translate }}
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
