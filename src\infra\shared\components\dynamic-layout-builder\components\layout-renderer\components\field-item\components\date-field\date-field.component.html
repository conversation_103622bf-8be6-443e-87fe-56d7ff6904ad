<!-- Date Field Container -->
@if (shouldShowField()) {
  <div class="date-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display formatted mock data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="field-type-icon me-2">{{ getFieldIcon() }}</mat-icon>
          <span class="mock-value formatted-date">{{ mockValue() }}</span>
        </div>
      }
      
      <!-- FORM MODE: Display date picker control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Field icon prefix -->
          <mat-icon matIconPrefix class="field-type-icon">{{ getFieldIcon() }}</mat-icon>
          
          <!-- Date input element -->
          <input 
            matInput
            [matDatepicker]="picker"
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnly()"
            [class.read-only-cursor]="isReadOnly()"
            (dateChange)="onDateChange($event)">
          
          <!-- Date picker toggle -->
          <mat-datepicker-toggle
            matIconSuffix
            [for]="picker">
          </mat-datepicker-toggle>

          <!-- Date picker -->
          <mat-datepicker #picker>
          </mat-datepicker>
          
          <!-- Error messages -->
          @if (formControl().invalid && formControl().touched) {
            <mat-error>
              @if (formControl().hasError('required')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED' | translate }}
              }
              @else if (formControl().hasError('matDatepickerParse')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_DATE' | translate }}
              }
              @else if (formControl().hasError('matDatepickerMin')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.DATE_TOO_EARLY' | translate }}
              }
              @else if (formControl().hasError('matDatepickerMax')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.DATE_TOO_LATE' | translate }}
              }
              @else {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_DATE' | translate }}
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>

    <!-- Field Tooltip/Description -->
    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }

    <!-- Field Hints -->
    @if (config.currentViewMode === 'form' && isDateTimeField()) {
      <div class="field-hints">
        <small class="text-muted">
          {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_HINTS.DATETIME_FORMAT' | translate }}
        </small>
      </div>
    }
  </div>
}
