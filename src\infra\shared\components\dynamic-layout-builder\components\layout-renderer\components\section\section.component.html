<!-- Section Container -->
@if (hasVisibleFields()) {
  <mat-card class="section-card mb-4">
    
    <!-- Section Header -->
    @if (shouldShowTitle()) {
      <mat-card-header class="section-header">
        <mat-card-title class="section-title">
          <mat-icon class="section-icon me-2">folder</mat-icon>
          {{ config.section.title }}
        </mat-card-title>
      </mat-card-header>
    }

    <!-- Section Content -->
    <mat-card-content class="section-content">
      
      <!-- Single Column Layout -->
      @if (!isDoubleColumn()) {
        <div class="section-single-column">
          @for (field of visibleFields(); track field._id) {
            <app-field-item
              [config]="createFieldItemConfig(field)"
              (valueChange)="onFieldValueChange($event)"
              class="field-item">
            </app-field-item>
          }
        </div>
      }
      
      <!-- Double Column Layout -->
      @else {
        <div class="section-double-column">
          
          <!-- Left Column -->
          <div class="column-left">
            @for (field of leftColumnFields(); track field._id) {
              <app-field-item
                [config]="createFieldItemConfig(field)"
                (valueChange)="onFieldValueChange($event)"
                class="field-item">
              </app-field-item>
            }
          </div>
          
          <!-- Right Column -->
          <div class="column-right">
            @for (field of rightColumnFields(); track field._id) {
              <app-field-item
                [config]="createFieldItemConfig(field)"
                (valueChange)="onFieldValueChange($event)"
                class="field-item">
              </app-field-item>
            }
          </div>
        </div>
      }
    </mat-card-content>
  </mat-card>
}
