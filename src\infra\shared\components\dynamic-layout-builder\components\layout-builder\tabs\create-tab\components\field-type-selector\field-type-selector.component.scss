.field-type-selector-container {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  .selector-header {
    margin-bottom: 16px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .selector-description {
      margin: 0;
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }
  }

  .field-types-list {
    .field-group {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 16px;

      .group-title {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 500;
        color: #555;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .field-type-item {
        display: flex;
        width: 48%;
        margin: 1%;
        align-items: center;
        padding: 7px 12px 4px 12px;
        margin-bottom: 8px;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        transition: all 0.2s ease;
        position: relative;
        cursor: grab;
        user-select: none;

        &:hover {
          background: #f5f5f5;
          border-color: #2196f3;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        // Drag handle
        .drag-handle {
          color: #999;
          margin-right: 8px;
          cursor: grab;
          font-size: 18px;

          &:hover {
            color: #666;
          }
        }

        // Field icon
        .field-icon {
          margin-right: 12px;
          font-size: 20px;
          width: 20px;
          height: 20px;
          color: #2196f3;

          // Icon colors by field type
          &.field-icon-text { color: #4caf50; }
          &.field-icon-number { color: #ff9800; }
          &.field-icon-email { color: #2196f3; }
          &.field-icon-phone { color: #9c27b0; }
          &.field-icon-textarea { color: #607d8b; }
          &.field-icon-date { color: #f44336; }
          &.field-icon-datetime { color: #e91e63; }
          &.field-icon-file { color: #795548; }
          &.field-icon-image { color: #ff5722; }
          &.field-icon-checkbox { color: #8bc34a; }
          &.field-icon-radio { color: #cddc39; }
          &.field-icon-select { color: #00bcd4; }
          &.field-icon-size { color: #3f51b5; }
          &.field-icon-color { color: #e91e63; }
          &.field-icon-brand { color: #673ab7; }
          &.field-icon-category { color: #009688; }
        }

        // Field info
        .field-info {
          flex: 1;
          display: flex;
          flex-direction: column;

          .field-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
          }

          .field-description {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
          }
        }

        // Add button
        .add-btn {
          opacity: 0;
          transition: opacity 0.2s ease;
          color: #2196f3;

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }

        &:hover .add-btn {
          opacity: 1;
        }
      }
    }

    // Divider styling
    mat-divider {
      margin: 16px 0;
      border-color: #e0e0e0;
    }
  }
}

// Drag preview styling
.cdk-drag-preview {
  .field-type-item {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-color: #2196f3;
    background: white;
    transform: rotate(2deg);
  }
}

// Drag placeholder
.cdk-drag-placeholder {
  opacity: 0.4;
  border: 2px dashed #ccc;
  background: transparent;
}

// Responsive design
@media (max-width: 768px) {
  .field-type-selector-container {
    padding: 12px;

    .field-types-list {
      .field-group {
        .field-type-item {
          padding: 10px;

          .field-icon {
            margin-right: 10px;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }

          .field-info {
            .field-label {
              font-size: 13px;
            }

            .field-description {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// Drag & Drop Visual Feedback
.field-type-item {
  transition: all 0.2s ease;

  &.cdk-drag-dragging {
    opacity: 0.7;
    transform: rotate(5deg);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1000;
  }

  &.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .drag-handle {
    opacity: 0.6;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }
  }
}

// Drag preview styling
.field-drag-preview {
  .preview-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border: 2px solid #007bff;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-weight: 500;
    color: #007bff;

    .preview-icon {
      font-size: 18px;
    }

    .preview-label {
      white-space: nowrap;
    }
  }
}

// Global drag states
:global(body.field-dragging) {
  cursor: grabbing !important;

  .field-list {
    border: 2px dashed #007bff;
    background-color: rgba(0, 123, 255, 0.05);

    &.cdk-drop-list-dragging {
      background-color: rgba(0, 123, 255, 0.1);
    }
  }

  .section-container {
    &.valid-drop-zone {
      border-color: #28a745;
      background-color: rgba(40, 167, 69, 0.05);
    }

    &.invalid-drop-zone {
      border-color: #dc3545;
      background-color: rgba(220, 53, 69, 0.05);
      cursor: not-allowed;
    }
  }
}

// Animation for field type items
@keyframes fieldTypeItemAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.field-type-item {
  animation: fieldTypeItemAppear 0.3s ease-out;
}

// Stagger animation for multiple items
.field-type-item:nth-child(1) { animation-delay: 0.1s; }
.field-type-item:nth-child(2) { animation-delay: 0.15s; }
.field-type-item:nth-child(3) { animation-delay: 0.2s; }
.field-type-item:nth-child(4) { animation-delay: 0.25s; }
.field-type-item:nth-child(5) { animation-delay: 0.3s; }
.field-type-item:nth-child(6) { animation-delay: 0.35s; }
.field-type-item:nth-child(7) { animation-delay: 0.4s; }
.field-type-item:nth-child(8) { animation-delay: 0.45s; }
.field-type-item:nth-child(9) { animation-delay: 0.5s; }
.field-type-item:nth-child(10) { animation-delay: 0.55s; }
.field-type-item:nth-child(11) { animation-delay: .6s; }
.field-type-item:nth-child(12) { animation-delay: .65s; }
.field-type-item:nth-child(13) { animation-delay: .7s; }
.field-type-item:nth-child(14) { animation-delay: .75s; }
.field-type-item:nth-child(15) { animation-delay: .8s; }
.field-type-item:nth-child(16) { animation-delay: .85s; }
