import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef, OnInit, OnChanges, OnDestroy, SimpleChanges, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MockDataService } from '../../../../services';
import { BaseFieldComponent, FormFieldComponent, TextFieldTypes, FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Component chuyên bi<PERSON>t xử lý các text-based fields
 * 
 * Supported field types:
 * - text: <PERSON><PERSON><PERSON> bản thông thường
 * - email: Email với validation
 * - phone: Số điện thoại
 * - url: URL với validation
 * - search: Tìm kiếm
 * 
 * Features:
 * - View mode: Hiển thị mock data
 * - Form mode: Input controls với validation
 * - Permission-based visibility và read-only state
 * - i18n support cho placeholders
 * - Responsive design
 */
@Component({
  selector: 'app-text-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './text-field.component.html',
  styleUrls: ['./text-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MockDataService]
})
export class TextFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitter cho form value changes
   * Emit khi user thay đổi giá trị trong form mode
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  // Inject services
  private mockDataService = inject(MockDataService);
  private cdr = inject(ChangeDetectorRef);

  // Subscription để quản lý form control changes
  private subscriptions = new Subscription();

  // Signals để quản lý state
  mockValue = signal<string>('');
  formControl = signal<FormControl>(new FormControl());
  isVisible = signal<boolean>(true);
  isReadOnlyState = signal<boolean>(false);

  // Form mode state
  private currentValue = signal<FieldValue>('');
  private validationErrors = signal<string[]>([]);

  ngOnInit(): void {
    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi (ví dụ: permission hoặc view mode), cập nhật lại field
    if (changes['config']) {
      this.initializeField();
    }
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  /**
   * Khởi tạo field dựa trên config
   */
  private initializeField(): void {
    // Validate field type
    this.validateFieldType();
    
    // Kiểm tra visibility dựa trên permission
    this.isVisible.set(this.config.currentPermission !== 'none');
    
    // Kiểm tra read-only state
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );



    // Generate mock data cho view mode
    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    // Khởi tạo form control cho form mode
    if (this.config.currentViewMode === 'form') {
      // Hủy subscription cũ trước khi tạo form control mới
      this.subscriptions.unsubscribe();
      this.subscriptions = new Subscription();

      this.initializeFormControl();
      this.setupFormValueSubscription();
    }
  }

  /**
   * Validate rằng field type được hỗ trợ
   */
  private validateFieldType(): void {
    const supportedTypes: TextFieldTypes[] = ['text', 'email', 'phone', 'url', 'search'];
    if (!supportedTypes.includes(this.config.field.type as TextFieldTypes)) {
      console.warn(`TextFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value dựa trên field type
   */
  private generateMockValue(): void {
    const fieldType = this.config.field.type;
    this.mockValue.set(this.mockDataService.generateMockData(fieldType));
  }

  /**
   * Khởi tạo form control cho form mode
   */
  initializeFormControl(): void {
    // Lấy giá trị từ formValues trong config (Form Edit Mode)
    const fieldId = this.config.field._id || '';
    const formValue = this.config.formValues?.[fieldId];

    // Sử dụng form value, current value, hoặc default value
    const initialValue = formValue || this.currentValue() || this.config.field.defaultValue || '';

    // Cập nhật currentValue signal để đồng bộ
    this.currentValue.set(initialValue);

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    // Thêm validators dựa trên field type và requirements
    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  private getValidators(): any[] {
    const validators: any[] = [];

    // Required validator
    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.required);
    }

    // Type-specific validators
    switch (this.config.field.type) {
      case 'email':
        validators.push(Validators.email);
        break;
      case 'url':
        validators.push(Validators.pattern(/^https?:\/\/.+/));
        break;
      case 'phone':
        validators.push(Validators.pattern(/^[\d\s\-\+\(\)]+$/));
        break;
    }

    // Length validators
    // Kiểm tra minLength nếu có trong constraints
    if (this.config.field.constraints && 'minLength' in this.config.field.constraints) {
      const constraints = this.config.field.constraints as { minLength?: number };
      if (constraints.minLength) {
        validators.push(Validators.minLength(constraints.minLength));
      }
    }
    // TextField có maxLength trong constraints
    if (this.config.field.constraints && 'maxLength' in this.config.field.constraints) {
      const constraints = this.config.field.constraints as { maxLength?: number };
      if (constraints.maxLength) {
        validators.push(Validators.maxLength(constraints.maxLength));
      }
    }

    return validators;
  }

  /**
   * Kiểm tra xem có nên hiển thị field hay không
   */
  shouldShowField(): boolean {
    return this.isVisible();
  }

  /**
   * Kiểm tra xem field có ở chế độ read-only không
   */
  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  /**
   * Lấy HTML input type phù hợp
   */
  getInputType(): string {
    switch (this.config.field.type) {
      case 'email':
        return 'email';
      case 'phone':
        return 'tel';
      case 'url':
        return 'url';
      case 'search':
        return 'search';
      default:
        return 'text';
    }
  }

  /**
   * Lấy placeholder text cho field
   */
  getPlaceholder(): string {
    return this.config.field.placeholder || 
           `DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.${this.config.field.type.toUpperCase()}`;
  }

  /**
   * Lấy tooltip text cho read-only fields
   */
  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  // ===== FORM MODE METHODS (BaseFieldComponent interface) =====

  /**
   * Lấy giá trị hiện tại của field
   */
  getCurrentValue(): FieldValue {
    if (this.config.currentViewMode === 'form') {
      return this.formControl().value;
    }
    return this.currentValue();
  }

  /**
   * Kiểm tra xem field có validation errors không
   */
  hasValidationErrors(): boolean {
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      return control.invalid && (control.dirty || control.touched);
    }
    return this.validationErrors().length > 0;
  }

  /**
   * Lấy danh sách validation errors
   */
  getValidationErrors(): string[] {
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      const errors: string[] = [];

      if (control.errors) {
        if (control.errors['required']) {
          errors.push('FORM_VALIDATION.FIELD_REQUIRED');
        }
        if (control.errors['email']) {
          errors.push('FORM_VALIDATION.INVALID_EMAIL');
        }
        if (control.errors['pattern']) {
          errors.push('FORM_VALIDATION.INVALID_FORMAT');
        }
        if (control.errors['minlength']) {
          errors.push('FORM_VALIDATION.TEXT_TOO_SHORT');
        }
        if (control.errors['maxlength']) {
          errors.push('FORM_VALIDATION.TEXT_TOO_LONG');
        }
      }

      return errors;
    }
    return this.validationErrors();
  }

  // ===== FORM FIELD COMPONENT METHODS =====

  /**
   * Bind giá trị từ form data vào field
   */
  bindFormValue(value: FieldValue): void {
    this.currentValue.set(value);

    // Nếu đang ở form mode, cập nhật form control
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      control.setValue(value, { emitEvent: false });
    }
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  private setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      this.onValueChange(value);
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Handle khi user thay đổi giá trị field
   */
  onValueChange(value: FieldValue): void {
    this.currentValue.set(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });

    // Trigger change detection để cập nhật UI
    this.cdr.markForCheck();
  }

  /**
   * Lấy icon phù hợp cho field type (để hiển thị trong form field)
   */
  getFieldIcon(): string {
    switch (this.config.field.type) {
      case 'email':
        return 'email';
      case 'phone':
        return 'phone';
      case 'url':
        return 'link';
      case 'search':
        return 'search';
      default:
        return 'text_fields';
    }
  }
}
