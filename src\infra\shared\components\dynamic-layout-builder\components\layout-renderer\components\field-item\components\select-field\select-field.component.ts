import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef, OnInit, OnChanges, OnDestroy, SimpleChanges, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MockDataService } from '../../../../services';
import { BaseFieldComponent, FormFieldComponent, SelectFieldTypes, FieldItemConfig, FieldValidationResult } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Component chuyên biệt xử lý các select-based fields
 * 
 * Supported field types:
 * - picklist: Dropdown chọn một giá trị
 * - multi-picklist: Multi-select chọn nhiều giá trị
 * - select: Standard select dropdown
 * - radio: Radio button group (rendered as select for consistency)
 * 
 * Features:
 * - View mode: Hiển thị mock data với chips cho multi-select
 * - Form mode: Select controls với search functionality
 * - Multi-select với chips display
 * - Mock options cho demo
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-select-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatChipsModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './select-field.component.html',
  styleUrls: ['./select-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MockDataService]
})
export class SelectFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  // Inject services
  private mockDataService = inject(MockDataService);
  private cdr = inject(ChangeDetectorRef);

  // Subscription để quản lý form control changes
  private subscriptions = new Subscription();

  // Signals để quản lý state
  mockValue = signal<string>('');
  mockValues = signal<string[]>([]);
  formControl = signal<FormControl>(new FormControl());
  isVisible = signal<boolean>(true);
  isReadOnlyState = signal<boolean>(false);

  // Mock options cho demo
  mockOptions = signal<Array<{value: string, label: string}>>([]);

  ngOnInit(): void {
    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi (ví dụ: permission hoặc view mode), cập nhật lại field
    if (changes['config']) {
      this.initializeField();
    }
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  /**
   * Khởi tạo field dựa trên config
   */
  private initializeField(): void {
    // Validate field type
    this.validateFieldType();

    // Kiểm tra visibility dựa trên permission
    this.isVisible.set(this.config.currentPermission !== 'none');

    // Kiểm tra read-only state
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );

    // Generate mock options
    this.generateMockOptions();

    // Generate mock data cho view mode
    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    // Khởi tạo form control cho form mode
    if (this.config.currentViewMode === 'form') {
      // Hủy subscription cũ trước khi tạo form control mới
      this.subscriptions.unsubscribe();
      this.subscriptions = new Subscription();

      this.initializeFormControl();
      this.setupFormValueSubscription();
    }
  }

  /**
   * Validate rằng field type được hỗ trợ
   */
  private validateFieldType(): void {
    const supportedTypes: SelectFieldTypes[] = ['picklist', 'multi-picklist', 'select', 'radio'];
    if (!supportedTypes.includes(this.config.field.type as SelectFieldTypes)) {
      console.warn(`SelectFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock options cho select
   */
  private generateMockOptions(): void {
    const options = [
      { value: 'option1', label: 'DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.SELECT_OPTION_1' },
      { value: 'option2', label: 'DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.SELECT_OPTION_2' },
      { value: 'option3', label: 'DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.SELECT_OPTION_3' },
      { value: 'option4', label: 'DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.SELECT_OPTION_4' },
      { value: 'option5', label: 'DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.SELECT_OPTION_5' }
    ];
    
    this.mockOptions.set(options);
  }

  /**
   * Generate mock value dựa trên field type
   */
  private generateMockValue(): void {
    const fieldType = this.config.field.type;
    
    if (this.isMultiSelect()) {
      // Multi-select: chọn 2-3 options ngẫu nhiên
      const selectedOptions = this.mockOptions().slice(0, Math.floor(Math.random() * 3) + 1);
      this.mockValues.set(selectedOptions.map(opt => opt.label));
    } else {
      // Single select: chọn 1 option ngẫu nhiên
      const randomOption = this.mockOptions()[Math.floor(Math.random() * this.mockOptions().length)];
      this.mockValue.set(randomOption?.label || '');
    }
  }

  /**
   * Khởi tạo form control cho form mode
   */
  initializeFormControl(): void {
    // Lấy initial value từ config hoặc form values
    const initialValue = this.getInitialValue();

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    // Thêm validators
    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);

    // Validate initial value và emit validation result
    this.validateAndEmit(initialValue);
  }

  /**
   * Lấy initial value cho form control
   */
  private getInitialValue(): FieldValue {
    const fieldId = this.config.field._id || '';

    // Ưu tiên lấy từ formValues trong config
    if (this.config.formValues && fieldId in this.config.formValues) {
      return this.config.formValues[fieldId];
    }

    // Fallback về defaultValue hoặc empty value
    return this.config.field.defaultValue || (this.isMultiSelect() ? [] : '');
  }

  /**
   * Lấy validators phù hợp cho select field
   */
  private getValidators(): any[] {
    const validators: any[] = [];

    // Required validator
    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.required);
    }

    return validators;
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  private setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      console.log('Form control value changed:', value);
      this.onValueChange(value);
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Xử lý khi form control value thay đổi
   * Public method theo yêu cầu của FormFieldComponent interface
   */
  onValueChange(value: FieldValue): void {
    const fieldId = this.config.field._id || '';

    // Emit value change event
    this.valueChange.emit({ fieldId, value });

    // Validate và emit validation result
    this.validateAndEmit(value);
  }

  /**
   * Validate field value và emit validation result
   */
  private validateAndEmit(value: FieldValue): void {
    const fieldId = this.config.field._id || '';
    const validation = this.validateFieldValue(value);

    // Emit validation change event
    this.validationChange.emit({ fieldId, validation });
  }

  /**
   * Validate field value và trả về validation result
   */
  private validateFieldValue(value: FieldValue): FieldValidationResult {
    const field = this.config.field;

    // Kiểm tra required
    if ((field.isRequired || field.required)) {
      if (this.isMultiSelect()) {
        // Multi-select: kiểm tra array có empty không
        if (!value || (Array.isArray(value) && value.length === 0)) {
          return {
            isValid: false,
            errorMessage: 'FORM_VALIDATION.FIELD_REQUIRED'
          };
        }
      } else {
        // Single select: kiểm tra value có empty không
        if (!value || value === '') {
          return {
            isValid: false,
            errorMessage: 'FORM_VALIDATION.FIELD_REQUIRED'
          };
        }
      }
    }

    // Nếu không có lỗi
    return { isValid: true };
  }

  /**
   * Kiểm tra xem có nên hiển thị field hay không
   */
  shouldShowField(): boolean {
    return this.isVisible();
  }

  /**
   * Kiểm tra xem field có ở chế độ read-only không
   */
  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  /**
   * Kiểm tra xem có phải multi-select không
   */
  isMultiSelect(): boolean {
    return this.config.field.type === 'multi-picklist';
  }

  /**
   * Lấy placeholder text cho field
   */
  getPlaceholder(): string {
    return this.config.field.placeholder || 
           `DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.${this.config.field.type.toUpperCase()}`;
  }

  /**
   * Lấy tooltip text cho read-only fields
   */
  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    const fieldType = this.config.field.type as SelectFieldTypes;
    switch (fieldType) {
      case 'multi-picklist':
        return 'checklist';
      case 'radio':
        return 'radio_button_checked';
      default:
        return 'arrow_drop_down';
    }
  }

  /**
   * Xử lý khi selection thay đổi
   */
  onSelectionChange(event: any): void {
    // Log selection change for debugging
    console.log('Selection changed:', event.value);

    // Angular Material Select đã tự động cập nhật form control
    // Chúng ta chỉ cần emit value change event và trigger change detection
    const fieldId = this.config.field._id || '';
    const value = event.value;

    // Emit value change event
    this.valueChange.emit({ fieldId, value });

    // Validate và emit validation result
    this.validateAndEmit(value);

    // Trigger change detection để cập nhật UI
    this.cdr.markForCheck();
  }

  /**
   * Remove chip trong multi-select mode
   */
  removeChip(chipValue: string): void {
    if (this.isReadOnly()) return;

    const currentValue = this.formControl().value || [];
    const newValue = currentValue.filter((value: string) => value !== chipValue);
    this.formControl().setValue(newValue);
  }

  /**
   * Lấy label của option dựa trên value
   */
  getOptionLabel(value: string): string {
    // Tạm thời trả về value, có thể cải thiện sau
    return value || '';
  }
}
