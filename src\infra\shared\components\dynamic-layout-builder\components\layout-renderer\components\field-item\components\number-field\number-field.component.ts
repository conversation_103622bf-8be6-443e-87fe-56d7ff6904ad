import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef, OnInit, OnChanges, OnDestroy, SimpleChanges, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MockDataService } from '../../../../services';
import { BaseFieldComponent, FormFieldComponent, NumberFieldTypes, FieldItemConfig, FieldValidationResult, FieldValue } from '../../../../../../models/dynamic-layout-renderer.model';

/**
 * Component chuyên biệt xử lý các number-based fields
 * 
 * Supported field types:
 * - number: Số nguyên
 * - decimal: Số thập phân
 * - currency: Tiền tệ với formatting
 * - percent: Phần trăm với % symbol
 * 
 * Features:
 * - View mode: Hiển thị mock data với formatting
 * - Form mode: Number input với validation
 * - Currency formatting (VND)
 * - Percent formatting với % symbol
 * - Min/max validation
 * - Step validation cho decimal
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-number-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './number-field.component.html',
  styleUrls: ['./number-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MockDataService]
})
export class NumberFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  // Inject services
  private mockDataService = inject(MockDataService);
  private cdr = inject(ChangeDetectorRef);

  // Signals để quản lý state
  mockValue = signal<string>('');
  formControl = signal<FormControl>(new FormControl());
  isVisible = signal<boolean>(true);
  isReadOnlyState = signal<boolean>(false);

  // Subscription để quản lý form control changes
  private subscriptions = new Subscription();

  ngOnInit(): void {
    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi (ví dụ: permission hoặc view mode), cập nhật lại field
    if (changes['config']) {
      this.initializeField();
    }
  }

  /**
   * Khởi tạo field dựa trên config
   */
  private initializeField(): void {
    // Validate field type
    this.validateFieldType();
    
    // Kiểm tra visibility dựa trên permission
    this.isVisible.set(this.config.currentPermission !== 'none');
    
    // Kiểm tra read-only state
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );

    // Generate mock data cho view mode
    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    // Khởi tạo form control cho form mode
    if (this.config.currentViewMode === 'form') {
      // Hủy subscription cũ trước khi tạo form control mới
      this.subscriptions.unsubscribe();
      this.subscriptions = new Subscription();

      this.initializeFormControl();
      this.setupFormValueSubscription();
    }
  }

  /**
   * Validate rằng field type được hỗ trợ
   */
  private validateFieldType(): void {
    const supportedTypes: NumberFieldTypes[] = ['number', 'decimal', 'currency', 'percent'];
    if (!supportedTypes.includes(this.config.field.type as NumberFieldTypes)) {
      console.warn(`NumberFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value với formatting phù hợp
   */
  private generateMockValue(): void {
    const rawValue = this.mockDataService.generateMockData(this.config.field.type);
    const numericValue = parseFloat(rawValue) || 0;
    
    // Format dựa trên field type
    this.mockValue.set(this.formatDisplayValue(numericValue));
  }

  /**
   * Format giá trị để hiển thị dựa trên field type
   */
  private formatDisplayValue(value: number): string {
    switch (this.config.field.type) {
      case 'currency':
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(value);
      
      case 'percent':
        return `${value}%`;
      
      case 'decimal':
        return value.toFixed(2);
      
      case 'number':
      default:
        return value.toString();
    }
  }

  /**
   * Khởi tạo form control cho form mode
   */
  initializeFormControl(): void {
    // Lấy initial value từ formValues hoặc defaultValue
    const fieldId = this.config.field._id || '';
    const initialValue = this.config.formValues?.[fieldId] || this.config.field.defaultValue || '';

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    // Thêm validators
    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);
  }

  /**
   * Lấy validators phù hợp cho number field
   */
  private getValidators(): any[] {
    const validators: any[] = [];

    // Required validator
    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.required);
    }

    // Min/Max validators từ constraints
    if (this.config.field.constraints) {
      const constraints = this.config.field.constraints as any;
      if (constraints.min !== undefined) {
        validators.push(Validators.min(constraints.min));
      }
      if (constraints.max !== undefined) {
        validators.push(Validators.max(constraints.max));
      }
    }

    // Pattern validator cho percent (0-100)
    if (this.config.field.type === 'percent') {
      validators.push(Validators.min(0));
      validators.push(Validators.max(100));
    }

    // Pattern validator cho currency (positive numbers)
    if (this.config.field.type === 'currency') {
      validators.push(Validators.min(0));
    }

    return validators;
  }

  /**
   * Kiểm tra xem có nên hiển thị field hay không
   */
  shouldShowField(): boolean {
    return this.isVisible();
  }

  /**
   * Kiểm tra xem field có ở chế độ read-only không
   */
  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  /**
   * Lấy step value cho input
   */
  getStepValue(): string {
    switch (this.config.field.type) {
      case 'decimal':
        return '0.01';
      case 'currency':
        return '1000'; // Step 1000 VND
      case 'percent':
        return '0.1';
      default:
        return '1';
    }
  }

  /**
   * Lấy placeholder text cho field
   */
  getPlaceholder(): string {
    return this.config.field.placeholder || 
           `DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.${this.config.field.type.toUpperCase()}`;
  }

  /**
   * Lấy tooltip text cho read-only fields
   */
  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    switch (this.config.field.type) {
      case 'currency':
        return 'attach_money';
      case 'percent':
        return 'percent';
      case 'decimal':
        return 'decimal_increase';
      default:
        return 'numbers';
    }
  }

  /**
   * Lấy suffix text cho input
   */
  getSuffixText(): string {
    switch (this.config.field.type) {
      case 'percent':
        return '%';
      case 'currency':
        return 'VND';
      default:
        return '';
    }
  }

  /**
   * Lấy giá trị min từ constraints
   */
  getMinValue(): number | undefined {
    if (this.config.field.constraints) {
      const constraints = this.config.field.constraints as any;
      return constraints.min;
    }
    return undefined;
  }

  /**
   * Lấy giá trị max từ constraints
   */
  getMaxValue(): number | undefined {
    if (this.config.field.constraints) {
      const constraints = this.config.field.constraints as any;
      return constraints.max;
    }
    return undefined;
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  private setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      this.onValueChange(value);
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Xử lý khi form control value thay đổi
   * Public method theo yêu cầu của FormFieldComponent interface
   */
  onValueChange(value: FieldValue): void {
    const fieldId = this.config.field._id || '';

    // Emit value change event
    this.valueChange.emit({ fieldId, value });

    // Validate và emit validation result
    this.validateAndEmit(value);

    // Trigger change detection để cập nhật UI
    this.cdr.markForCheck();
  }

  /**
   * Validate field value và emit validation result
   */
  private validateAndEmit(value: FieldValue): void {
    const fieldId = this.config.field._id || '';
    const validation = this.validateFieldValue(value);

    // Emit validation change event
    this.validationChange.emit({ fieldId, validation });
  }

  /**
   * Validate field value và trả về validation result
   */
  private validateFieldValue(value: FieldValue): FieldValidationResult {
    const field = this.config.field;

    // Kiểm tra required
    if ((field.isRequired || field.required)) {
      if (!value || value === '' || value === null || value === undefined) {
        return {
          isValid: false,
          errorMessage: 'FORM_VALIDATION.FIELD_REQUIRED'
        };
      }
    }

    // Kiểm tra min/max từ constraints
    if (field.constraints && value !== null && value !== undefined && value !== '') {
      const constraints = field.constraints as any;
      const numericValue = parseFloat(value as string);

      if (!isNaN(numericValue)) {
        if (constraints.min !== undefined && numericValue < constraints.min) {
          return {
            isValid: false,
            errorMessage: 'FORM_VALIDATION.NUMBER_TOO_SMALL'
          };
        }

        if (constraints.max !== undefined && numericValue > constraints.max) {
          return {
            isValid: false,
            errorMessage: 'FORM_VALIDATION.NUMBER_TOO_LARGE'
          };
        }
      }
    }

    // Nếu không có lỗi
    return { isValid: true };
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }
}
