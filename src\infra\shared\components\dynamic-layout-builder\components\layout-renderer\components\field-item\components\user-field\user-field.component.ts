import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnChanges, OnDestroy, SimpleChanges, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MockDataService } from '../../../../services';
import { BaseFieldComponent, FormFieldComponent, FieldItemConfig, FieldValidationResult } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

@Component({
  selector: 'app-user-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatTooltipModule,
    MatChipsModule,
    TranslateModule
  ],
  templateUrl: './user-field.component.html',
  styleUrls: ['./user-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MockDataService]
})
export class UserFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  private mockDataService = inject(MockDataService);

  // Subscription để quản lý form control changes
  private subscriptions = new Subscription();

  mockValue = signal<string>('');
  formControl = signal<FormControl>(new FormControl());
  isVisible = signal<boolean>(true);
  isReadOnlyState = signal<boolean>(false);
  
  mockUsers = signal<Array<{id: string, name: string, email: string}>>([]);

  ngOnInit(): void {
    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi (ví dụ: permission hoặc view mode), cập nhật lại field
    if (changes['config']) {
      this.initializeField();
    }
  }

  private initializeField(): void {
    this.isVisible.set(this.config.currentPermission !== 'none');
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );

    this.generateMockUsers();

    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    if (this.config.currentViewMode === 'form') {
      this.initializeFormControl();
    }
  }

  private generateMockUsers(): void {
    const users = [
      { id: 'user1', name: 'Nguyễn Văn A', email: '<EMAIL>' },
      { id: 'user2', name: 'Trần Thị B', email: '<EMAIL>' },
      { id: 'user3', name: 'Lê Văn C', email: '<EMAIL>' },
      { id: 'user4', name: 'Phạm Thị D', email: '<EMAIL>' },
      { id: 'user5', name: 'Hoàng Văn E', email: '<EMAIL>' }
    ];
    
    this.mockUsers.set(users);
  }

  private generateMockValue(): void {
    const randomUser = this.mockUsers()[Math.floor(Math.random() * this.mockUsers().length)];
    this.mockValue.set(randomUser?.name || '');
  }

  initializeFormControl(): void {
    // Lấy initial value từ formValues nếu có, nếu không thì dùng defaultValue
    const fieldId = this.config.field._id || '';
    const initialValue = this.config.formValues?.[fieldId] || this.config.field.defaultValue || '';

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);

    // Setup subscription để theo dõi form control value changes
    this.setupFormValueSubscription();
  }

  private getValidators(): any[] {
    const validators: any[] = [];

    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.required);
    }

    return validators;
  }

  shouldShowField(): boolean {
    return this.isVisible();
  }

  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  getPlaceholder(): string {
    return this.config.field.placeholder || 
           'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.USER';
  }

  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  onUserChange(event: any): void {
    console.log('User changed:', event.value);
    // Form control sẽ tự động trigger valueChanges subscription
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  private setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Hủy subscription cũ trước khi tạo mới
    this.subscriptions.unsubscribe();
    this.subscriptions = new Subscription();

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      this.onValueChange(value);
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Xử lý khi form control value thay đổi
   * Public method theo yêu cầu của FormFieldComponent interface
   */
  onValueChange(value: FieldValue): void {
    const fieldId = this.config.field._id || '';

    // Emit value change event
    this.valueChange.emit({ fieldId, value });

    // Validate và emit validation result
    this.validateAndEmit(value);
  }

  /**
   * Validate field value và emit validation result
   */
  private validateAndEmit(value: FieldValue): void {
    const validationResult = this.validateFieldValue(value);
    const fieldId = this.config.field._id || '';

    this.validationChange.emit({ fieldId, validation: validationResult });
  }

  /**
   * Validate field value và trả về validation result
   */
  private validateFieldValue(value: FieldValue): FieldValidationResult {
    const field = this.config.field;

    // Kiểm tra required cho user field
    if ((field.isRequired || field.required)) {
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        return {
          isValid: false,
          errorMessage: 'FORM_VALIDATION.FIELD_REQUIRED'
        };
      }
    }

    // Nếu không có lỗi
    return { isValid: true };
  }

  /**
   * Lấy email của user được chọn
   */
  getUserEmail(): string {
    const user = this.mockUsers().find(u => u.name === this.mockValue());
    return user?.email || '';
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }
}
