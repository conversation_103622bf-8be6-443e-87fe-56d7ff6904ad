<!-- Text Field Container -->
@if (shouldShowField()) {
  <div class="text-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display mock data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="field-type-icon me-2">{{ getFieldIcon() }}</mat-icon>
          <span class="mock-value">{{ mockValue() }}</span>
        </div>
      }
      
      <!-- FORM MODE: Display input control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Field icon prefix -->
          <mat-icon matIconPrefix class="field-type-icon">{{ getFieldIcon() }}</mat-icon>
          
          <!-- Input element -->
          <input 
            matInput
            [type]="getInputType()"
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnly()"
            [class.read-only-cursor]="isReadOnly()">
          
          <!-- Error messages -->
          @if (formControl().invalid && formControl().touched) {
            <mat-error>
              @if (formControl().hasError('required')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED' | translate }}
              }
              @else if (formControl().hasError('email')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_EMAIL' | translate }}
              }
              @else if (formControl().hasError('pattern')) {
                @switch (config.field.type) {
                  @case ('url') {
                    {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_URL' | translate }}
                  }
                  @case ('phone') {
                    {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_PHONE' | translate }}
                  }
                  @default {
                    {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_FORMAT' | translate }}
                  }
                }
              }
              @else if (formControl().hasError('minlength')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MIN_LENGTH' | translate: {min: formControl().getError('minlength').requiredLength} }}
              }
              @else if (formControl().hasError('maxlength')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_LENGTH' | translate: {max: formControl().getError('maxlength').requiredLength} }}
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>

    <!-- Field Tooltip/Description -->
    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
