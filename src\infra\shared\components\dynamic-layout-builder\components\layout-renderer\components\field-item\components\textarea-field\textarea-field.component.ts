import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnChanges, OnDestroy, SimpleChanges, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MockDataService } from '../../../../services';
import { BaseFieldComponent, FormFieldComponent, FieldItemConfig, FieldValidationResult } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Component chuyên biệt xử lý textarea fields
 * 
 * Features:
 * - View mode: Hi<PERSON>n thị mock data với line breaks
 * - Form mode: Textarea với auto-resize
 * - Character count display
 * - Min/max length validation
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-textarea-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './textarea-field.component.html',
  styleUrls: ['./textarea-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MockDataService]
})
export class TextareaFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  private mockDataService = inject(MockDataService);

  // Subscription để quản lý form control changes
  private subscriptions = new Subscription();

  mockValue = signal<string>('');
  formControl = signal<FormControl>(new FormControl());
  isVisible = signal<boolean>(true);
  isReadOnlyState = signal<boolean>(false);

  ngOnInit(): void {
    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi (ví dụ: permission hoặc view mode), cập nhật lại field
    if (changes['config']) {
      this.initializeField();
    }
  }

  private initializeField(): void {
    this.isVisible.set(this.config.currentPermission !== 'none');
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );

    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    if (this.config.currentViewMode === 'form') {
      this.initializeFormControl();
      this.setupFormValueSubscription();
    }
  }

  private generateMockValue(): void {
    this.mockValue.set(this.mockDataService.generateMockData('textarea'));
  }

  initializeFormControl(): void {
    const fieldId = this.config.field._id || '';
    const initialValue = this.config.formValues?.[fieldId] || this.config.field.defaultValue || '';

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);
  }

  private getValidators(): any[] {
    const validators: any[] = [];

    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.required);
    }

    // TextareaField có maxLength trong constraints
    if (this.config.field.type === 'textarea' && this.config.field.constraints) {
      const constraints = this.config.field.constraints as any;
      if (constraints.maxLength) {
        validators.push(Validators.maxLength(constraints.maxLength));
      }
    }

    return validators;
  }

  shouldShowField(): boolean {
    return this.isVisible();
  }

  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  getPlaceholder(): string {
    return this.config.field.placeholder || 
           'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.TEXTAREA';
  }

  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  getCharacterCount(): number {
    return this.formControl().value?.length || 0;
  }

  getMaxLength(): number {
    // TextareaField có maxLength trong constraints
    if (this.config.field.type === 'textarea' && this.config.field.constraints) {
      return (this.config.field.constraints as any).maxLength || 500;
    }
    return 500;
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  private setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Hủy subscription cũ trước khi tạo mới
    this.subscriptions.unsubscribe();
    this.subscriptions = new Subscription();

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      this.onValueChange(value);
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Xử lý khi form control value thay đổi
   * Public method theo yêu cầu của FormFieldComponent interface
   */
  onValueChange(value: FieldValue): void {
    const fieldId = this.config.field._id || '';

    // Emit value change event
    this.valueChange.emit({ fieldId, value });

    // Validate và emit validation result
    this.validateAndEmit(value);
  }

  /**
   * Validate field value và emit validation result
   */
  private validateAndEmit(value: FieldValue): void {
    const fieldId = this.config.field._id || '';
    const validationResult = this.validateFieldValue(value);
    this.validationChange.emit({ fieldId, validation: validationResult });
  }

  /**
   * Validate field value và trả về validation result
   */
  private validateFieldValue(value: FieldValue): FieldValidationResult {
    const field = this.config.field;

    // Kiểm tra required
    if ((field.isRequired || field.required)) {
      if (!value || value === '' || value === null || value === undefined) {
        return {
          isValid: false,
          errorMessage: 'FORM_VALIDATION.FIELD_REQUIRED'
        };
      }
    }

    // Kiểm tra maxLength cho textarea
    if (field.type === 'textarea' && field.constraints) {
      const constraints = field.constraints as any;
      if (constraints.maxLength && value && (value as string).length > constraints.maxLength) {
        return {
          isValid: false,
          errorMessage: 'FORM_VALIDATION.TEXT_TOO_LONG'
        };
      }
    }

    // Nếu không có lỗi
    return { isValid: true };
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }
}
