<!-- Checkbox Field Container -->
@if (shouldShowField()) {
  <div class="checkbox-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display mock data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="checkbox-icon me-2" [class.checked]="mockValue()">
            {{ mockValue() ? 'check_box' : 'check_box_outline_blank' }}
          </mat-icon>
          <span class="checkbox-label">
            {{ getCheckboxLabel() | translate }}
          </span>
          <span class="checkbox-status ms-2" [class.checked]="mockValue()">
            {{ mockValue() ? ('DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.CHECKBOX_CHECKED' | translate) : ('DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.CHECKBOX_UNCHECKED' | translate) }}
          </span>
        </div>
      }
      
      <!-- FORM MODE: Display checkbox control -->
      @else {
        <div class="checkbox-form-container">
          <mat-checkbox
            [formControl]="formControl()"
            class="field-checkbox"
            (change)="onCheckboxChange($event)">
            {{ getCheckboxLabel() | translate }}
          </mat-checkbox>
          
          <!-- Error messages -->
          @if (formControl().invalid && formControl().touched) {
            <div class="checkbox-error">
              @if (formControl().hasError('required')) {
                <small class="text-danger">
                  {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.CHECKBOX_REQUIRED' | translate }}
                </small>
              }
            </div>
          }
        </div>
      }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
