<!-- Number Field Container -->
@if (shouldShowField()) {
  <div class="number-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display formatted mock data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="field-type-icon me-2">{{ getFieldIcon() }}</mat-icon>
          <span class="mock-value formatted-number">{{ mockValue() }}</span>
        </div>
      }
      
      <!-- FORM MODE: Display number input control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Field icon prefix -->
          <mat-icon matIconPrefix class="field-type-icon">{{ getFieldIcon() }}</mat-icon>
          
          <!-- Number input element -->
          <input 
            matInput
            type="number"
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnly()"
            [step]="getStepValue()"
            [class.read-only-cursor]="isReadOnly()">
          
          <!-- Suffix text (%, VND, etc.) -->
          @if (getSuffixText()) {
            <span matTextSuffix class="number-suffix">{{ getSuffixText() }}</span>
          }
          
          <!-- Error messages -->
          @if (formControl().invalid && formControl().touched) {
            <mat-error>
              @if (formControl().hasError('required')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED' | translate }}
              }
              @else if (formControl().hasError('min')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MIN_VALUE' | translate: {min: formControl().getError('min').min} }}
              }
              @else if (formControl().hasError('max')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_VALUE' | translate: {max: formControl().getError('max').max} }}
              }
              @else {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_NUMBER' | translate }}
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>

    <!-- Field Tooltip/Description -->
    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }

    <!-- Field Hints (Min/Max values) -->
    @if (config.currentViewMode === 'form' && (getMinValue() !== undefined || getMaxValue() !== undefined)) {
      <div class="field-hints">
        <small class="text-muted">
          @if (getMinValue() !== undefined && getMaxValue() !== undefined) {
            {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_HINTS.NUMBER_RANGE' | translate: {min: getMinValue(), max: getMaxValue()} }}
          }
          @else if (getMinValue() !== undefined) {
            {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_HINTS.MIN_VALUE' | translate: {min: getMinValue()} }}
          }
          @else if (getMaxValue() !== undefined) {
            {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_HINTS.MAX_VALUE' | translate: {max: getMaxValue()} }}
          }
        </small>
      </div>
    }
  </div>
}
