import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnChanges, OnDestroy, SimpleChanges, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MockDataService } from '../../../../services';
import { BaseFieldComponent, FormFieldComponent, FieldItemConfig, FieldValidationResult } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Component chuyên bi<PERSON><PERSON> x<PERSON> lý checkbox fields
 * 
 * Features:
 * - View mode: Hiển thị mock data với check/uncheck icon
 * - Form mode: Checkbox control
 * - Permission-based visibility và read-only state
 * - i18n support
 * - Accessibility support
 */
@Component({
  selector: 'app-checkbox-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './checkbox-field.component.html',
  styleUrls: ['./checkbox-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MockDataService]
})
export class CheckboxFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  private mockDataService = inject(MockDataService);

  // Subscription để quản lý form control changes
  private subscriptions = new Subscription();

  mockValue = signal<boolean>(false);
  formControl = signal<FormControl>(new FormControl());
  isVisible = signal<boolean>(true);
  isReadOnlyState = signal<boolean>(false);

  ngOnInit(): void {
    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi (ví dụ: permission hoặc view mode), cập nhật lại field
    if (changes['config']) {
      this.initializeField();
    }
  }

  private initializeField(): void {
    this.isVisible.set(this.config.currentPermission !== 'none');
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );

    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    if (this.config.currentViewMode === 'form') {
      this.initializeFormControl();
    }
  }

  private generateMockValue(): void {
    const rawValue = this.mockDataService.generateBooleanMockData(Math.random() > 0.5);
    // Xử lý boolean value từ mock data
    if (typeof rawValue === 'boolean') {
      this.mockValue.set(rawValue);
    } else if (typeof rawValue === 'string') {
      this.mockValue.set(rawValue === 'true' || rawValue === 'True');
    } else {
      this.mockValue.set(false);
    }
  }

  initializeFormControl(): void {
    // Lấy initial value từ formValues hoặc defaultValue
    const fieldId = this.config.field._id || '';
    const initialValue = this.config.formValues?.[fieldId] ?? this.config.field.defaultValue ?? false;

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);

    // Setup subscription để theo dõi form control changes
    this.setupFormValueSubscription();
  }

  private getValidators(): any[] {
    const validators: any[] = [];

    // Required validator cho checkbox (phải được check)
    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.requiredTrue);
    }

    return validators;
  }

  shouldShowField(): boolean {
    return this.isVisible();
  }

  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  getPlaceholder(): string {
    return this.config.field.placeholder || 
           'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.CHECKBOX';
  }

  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  getCheckboxLabel(): string {
    return this.config.field.placeholder || this.getPlaceholder();
  }

  onCheckboxChange(event: any): void {
    // Log checkbox change for debugging
    console.log('Checkbox changed:', event.checked);
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  private setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Hủy subscription cũ trước khi tạo mới
    this.subscriptions.unsubscribe();
    this.subscriptions = new Subscription();

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      this.onValueChange(value);
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Xử lý khi form control value thay đổi
   * Public method theo yêu cầu của FormFieldComponent interface
   */
  onValueChange(value: FieldValue): void {
    const fieldId = this.config.field._id || '';

    // Emit value change event
    this.valueChange.emit({ fieldId, value });

    // Validate và emit validation result
    this.validateAndEmit(value);
  }

  /**
   * Validate field value và emit validation result
   */
  private validateAndEmit(value: FieldValue): void {
    const validationResult = this.validateFieldValue(value);
    const fieldId = this.config.field._id || '';
    this.validationChange.emit({ fieldId, validation: validationResult });
  }

  /**
   * Validate field value và trả về validation result
   */
  private validateFieldValue(value: FieldValue): FieldValidationResult {
    const field = this.config.field;

    // Kiểm tra required cho checkbox (phải được check = true)
    if ((field.isRequired || field.required)) {
      // Convert value to boolean để kiểm tra
      const boolValue = Boolean(value);
      if (!boolValue) {
        return {
          isValid: false,
          errorMessage: 'FORM_VALIDATION.FIELD_REQUIRED'
        };
      }
    }

    // Nếu không có lỗi
    return { isValid: true };
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }
}
