import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

// Import models và types
import {
  FieldItemConfig,
  FieldValue,
  FieldValidationResult,
  TextFieldTypes,
  NumberFieldTypes,
  SelectFieldTypes,
  DateFieldTypes,
  FileFieldTypes
} from '../../../../models/dynamic-layout-renderer.model';

// Import các specialized field components
import { TextFieldComponent } from './components/text-field/text-field.component';
import { NumberFieldComponent } from './components/number-field/number-field.component';
import { SelectFieldComponent } from './components/select-field/select-field.component';
import { DateFieldComponent } from './components/date-field/date-field.component';
import { TextareaFieldComponent } from './components/textarea-field/textarea-field.component';
import { CheckboxFieldComponent } from './components/checkbox-field/checkbox-field.component';
import { FileFieldComponent } from './components/file-field/file-field.component';
import { UserFieldComponent } from './components/user-field/user-field.component';

/**
 * Container component điều phối đến các specialized field components
 *
 * Refactored từ monolithic component thành container component sử dụng switch case
 * để điều phối đến các component con chuyên biệt cho từng nhóm field type.
 *
 * Features:
 * - Điều phối field rendering đến specialized components
 * - Hỗ trợ tất cả field types thông qua component composition
 * - Duy trì backward compatibility với existing API
 * - Clean separation of concerns
 */
@Component({
  selector: 'app-field-item',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    TextFieldComponent,
    NumberFieldComponent,
    SelectFieldComponent,
    DateFieldComponent,
    TextareaFieldComponent,
    CheckboxFieldComponent,
    FileFieldComponent,
    UserFieldComponent
  ],
  templateUrl: './field-item.component.html',
  styleUrls: ['./field-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FieldItemComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitter cho field value changes
   * Propagate events từ specialized field components lên section component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  /**
   * Event emitter cho field validation changes
   * Propagate validation events từ specialized field components lên section component
   */
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  /**
   * Xác định field type thuộc nhóm nào để điều phối đến component phù hợp
   */
  getFieldComponentType(): 'text' | 'number' | 'select' | 'date' | 'textarea' | 'checkbox' | 'file' | 'user' | 'unknown' {
    const fieldType = this.config.field.type;

    // Text-based fields
    const textTypes: TextFieldTypes[] = ['text', 'email', 'phone', 'url', 'search'];
    if (textTypes.includes(fieldType as TextFieldTypes)) {
      return 'text';
    }

    // Number-based fields
    const numberTypes: NumberFieldTypes[] = ['number', 'decimal', 'currency', 'percent'];
    if (numberTypes.includes(fieldType as NumberFieldTypes)) {
      return 'number';
    }

    // Select-based fields
    const selectTypes: SelectFieldTypes[] = ['picklist', 'multi-picklist', 'select', 'radio'];
    if (selectTypes.includes(fieldType as SelectFieldTypes)) {
      return 'select';
    }

    // Date-based fields
    const dateTypes: DateFieldTypes[] = ['date', 'datetime'];
    if (dateTypes.includes(fieldType as DateFieldTypes)) {
      return 'date';
    }

    // File-based fields
    const fileTypes: FileFieldTypes[] = ['file', 'image'];
    if (fileTypes.includes(fieldType as FileFieldTypes)) {
      return 'file';
    }

    // Specific field types
    if (fieldType === 'textarea') return 'textarea';
    if (fieldType === 'checkbox') return 'checkbox';
    if (fieldType === 'user') return 'user';

    // Unknown field type
    console.warn(`FieldItemComponent: Unknown field type '${fieldType}'`);
    return 'unknown';
  }

  /**
   * Handle field value change events từ specialized field components
   * Propagate event lên section component
   * @param event Event object chứa fieldId và value
   */
  onFieldValueChange(event: { fieldId: string; value: FieldValue }): void {
    // Propagate event lên section component
    this.valueChange.emit(event);
  }

  /**
   * Handle field validation change events từ specialized field components
   * Propagate event lên section component
   * @param event Event object chứa fieldId và validation result
   */
  onFieldValidationChange(event: { fieldId: string; validation: FieldValidationResult }): void {
    // Propagate event lên section component
    this.validationChange.emit(event);
  }

}
