import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnChanges, OnDestroy, SimpleChanges, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { MockDataService } from '../../../../services';
import { BaseFieldComponent, FormFieldComponent, DateFieldTypes, FieldItemConfig, FieldValidationResult } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Component chuyên biệt xử lý các date-based fields
 * 
 * Supported field types:
 * - date: Ngày (chỉ ngày)
 * - datetime: Ngày giờ (ngày + giờ)
 * 
 * Features:
 * - View mode: Hiển thị mock data với formatting
 * - Form mode: Date picker với validation
 * - Date formatting theo locale
 * - Min/max date validation
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-date-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './date-field.component.html',
  styleUrls: ['./date-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MockDataService]
})
export class DateFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  // Inject services
  private mockDataService = inject(MockDataService);

  // Signals để quản lý state
  mockValue = signal<string>('');
  formControl = signal<FormControl>(new FormControl());
  isVisible = signal<boolean>(true);
  isReadOnlyState = signal<boolean>(false);

  // Subscription để quản lý form control changes
  private subscriptions = new Subscription();

  ngOnInit(): void {
    this.initializeField();
    this.setupFormValueSubscription();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi (ví dụ: permission hoặc view mode), cập nhật lại field
    if (changes['config']) {
      this.initializeField();
      this.setupFormValueSubscription();
    }
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  /**
   * Khởi tạo field dựa trên config
   */
  private initializeField(): void {
    // Validate field type
    this.validateFieldType();
    
    // Kiểm tra visibility dựa trên permission
    this.isVisible.set(this.config.currentPermission !== 'none');
    
    // Kiểm tra read-only state
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );

    // Generate mock data cho view mode
    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    // Khởi tạo form control cho form mode
    if (this.config.currentViewMode === 'form') {
      this.initializeFormControl();
    }
  }

  /**
   * Validate rằng field type được hỗ trợ
   */
  private validateFieldType(): void {
    const supportedTypes: DateFieldTypes[] = ['date', 'datetime'];
    if (!supportedTypes.includes(this.config.field.type as DateFieldTypes)) {
      console.warn(`DateFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value với formatting phù hợp
   */
  private generateMockValue(): void {
    const rawValue = this.mockDataService.generateMockData(this.config.field.type);
    const date = new Date(rawValue);
    
    // Format dựa trên field type
    this.mockValue.set(this.formatDisplayValue(date));
  }

  /**
   * Format giá trị để hiển thị dựa trên field type
   */
  private formatDisplayValue(date: Date): string {
    if (!date || isNaN(date.getTime())) {
      return '';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    };

    if (this.config.field.type === 'datetime') {
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.hour12 = false;
    }

    return new Intl.DateTimeFormat('vi-VN', options).format(date);
  }

  /**
   * Khởi tạo form control cho form mode
   */
  initializeFormControl(): void {
    // Lấy initial value từ config hoặc form values
    const initialValue = this.getInitialValue();

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()  // Proper FormControl disabled state
    });

    // Thêm validators
    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);

    // Validate initial value và emit validation result
    this.validateAndEmit(initialValue);
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  private setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Hủy subscription cũ trước khi tạo mới
    this.subscriptions.unsubscribe();
    this.subscriptions = new Subscription();

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      this.onValueChange(value);
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Xử lý khi form control value thay đổi
   * Public method theo yêu cầu của FormFieldComponent interface
   */
  onValueChange(value: FieldValue): void {
    const fieldId = this.config.field._id || '';

    // Convert Date object to ISO string for consistency
    let processedValue: FieldValue = value;
    if (value instanceof Date) {
      processedValue = value.toISOString();
    }

    // Emit value change event
    this.valueChange.emit({ fieldId, value: processedValue });

    // Validate và emit validation result
    this.validateAndEmit(processedValue);
  }

  /**
   * Validate field value và emit validation result
   */
  private validateAndEmit(value: FieldValue): void {
    const fieldId = this.config.field._id || '';
    const validationResult = this.validateFieldValue(value);

    // Emit validation change event
    this.validationChange.emit({ fieldId, validation: validationResult });
  }

  /**
   * Validate field value và trả về validation result
   */
  private validateFieldValue(value: FieldValue): FieldValidationResult {
    const field = this.config.field;

    // Kiểm tra required
    if ((field.isRequired || field.required)) {
      if (!value || value === '' || value === null || value === undefined) {
        return {
          isValid: false,
          errorMessage: 'FORM_VALIDATION.FIELD_REQUIRED'
        };
      }
    }

    // Kiểm tra date validity
    if (value) {
      const date = value instanceof Date ? value : new Date(value as string);
      if (isNaN(date.getTime())) {
        return {
          isValid: false,
          errorMessage: 'FORM_VALIDATION.INVALID_DATE'
        };
      }
    }

    // Nếu không có lỗi
    return { isValid: true };
  }

  /**
   * Lấy initial value từ config
   */
  private getInitialValue(): Date | null {
    const fieldId = this.config.field._id || '';
    const formValue = this.config.formValues?.[fieldId];

    if (formValue && typeof formValue === 'string') {
      return new Date(formValue);
    }

    return null;
  }

  /**
   * Lấy validators phù hợp cho date field
   */
  private getValidators(): any[] {
    const validators: any[] = [];

    // Required validator
    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.required);
    }

    // Min/Max date validators có thể được thêm ở đây
    // if (this.config.field.minDate) {
    //   validators.push(this.minDateValidator(this.config.field.minDate));
    // }

    return validators;
  }

  /**
   * Kiểm tra xem có nên hiển thị field hay không
   */
  shouldShowField(): boolean {
    return this.isVisible();
  }

  /**
   * Kiểm tra xem field có ở chế độ read-only không
   */
  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  /**
   * Kiểm tra xem có phải datetime field không
   */
  isDateTimeField(): boolean {
    return this.config.field.type === 'datetime';
  }

  /**
   * Lấy placeholder text cho field
   */
  getPlaceholder(): string {
    return this.config.field.placeholder || 
           `DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.${this.config.field.type.toUpperCase()}`;
  }

  /**
   * Lấy tooltip text cho read-only fields
   */
  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    switch (this.config.field.type) {
      case 'datetime':
        return 'schedule';
      default:
        return 'calendar_today';
    }
  }

  /**
   * Xử lý khi date thay đổi từ date picker
   */
  onDateChange(event: any): void {
    // Gọi onValueChange để xử lý thống nhất
    this.onValueChange(event.value);
  }
}
