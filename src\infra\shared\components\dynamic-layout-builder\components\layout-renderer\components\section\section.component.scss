// Section Component Styles
.section-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  
  // Section Header Styles
  .section-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    margin: 0;
    
    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #495057;
      margin: 0;
      display: flex;
      align-items: center;
      
      .section-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        color: #6c757d;
      }
    }
  }
  
  // Section Content Styles
  .section-content {
    padding: 1.5rem;
  }
}

// Single Column Layout
.section-single-column {
  .field-item {
    width: 100%;
  }
}

// Double Column Layout
.section-double-column {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  
  .column-left,
  .column-right {
    .field-item {
      width: 100%;
    }
  }
}

// Responsive Design
@media (max-width: 992px) {
  .section-card {
    .section-header {
      padding: 0.875rem 1.25rem;
      
      .section-title {
        font-size: 1rem;
        
        .section-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }
    
    .section-content {
      padding: 1.25rem;
    }
  }
  
  // Force single column on tablet
  .section-double-column {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .section-card {
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    
    .section-header {
      padding: 0.75rem 1rem;
      
      .section-title {
        font-size: 0.95rem;
        
        .section-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }
    
    .section-content {
      padding: 1rem;
    }
  }
  
  // Ensure single column on mobile
  .section-double-column {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 576px) {
  .section-card {
    margin-bottom: 0.75rem;
    border-radius: 0.25rem;
    
    .section-header {
      padding: 0.625rem 0.875rem;
      
      .section-title {
        font-size: 0.9rem;
        
        .section-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }
    }
    
    .section-content {
      padding: 0.875rem;
    }
  }
  
  // Single column with reduced gap on small mobile
  .section-double-column {
    gap: 0.75rem;
  }
}

// Animation for layout changes
.section-double-column {
  transition: grid-template-columns 0.3s ease-in-out;
}

// Empty state styling
.section-card:empty {
  display: none;
}

// Field item spacing within sections
.field-item {
  &:last-child {
    margin-bottom: 0;
  }
}
