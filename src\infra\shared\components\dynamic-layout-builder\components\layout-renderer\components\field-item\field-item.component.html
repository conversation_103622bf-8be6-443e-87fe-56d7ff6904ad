<!-- Container Component đ<PERSON><PERSON><PERSON> phối đến specialized field components -->
<div class="field-item-container">

  <!-- Switch case điều phối đến component phù hợp -->
  @switch (getFieldComponentType()) {

    <!-- Text-based fields -->
    @case ('text') {
      <app-text-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)">
      </app-text-field>
    }

    <!-- Number-based fields -->
    @case ('number') {
      <app-number-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)"
        (validationChange)="onFieldValidationChange($event)">
      </app-number-field>
    }

    <!-- Select-based fields -->
    @case ('select') {
      <app-select-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)"
        (validationChange)="onFieldValidationChange($event)">
      </app-select-field>
    }

    <!-- Date-based fields -->
    @case ('date') {
      <app-date-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)"
        (validationChange)="onFieldValidationChange($event)">
      </app-date-field>
    }

    <!-- Textarea field -->
    @case ('textarea') {
      <app-textarea-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)"
        (validationChange)="onFieldValidationChange($event)">
      </app-textarea-field>
    }

    <!-- Checkbox field -->
    @case ('checkbox') {
      <app-checkbox-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)"
        (validationChange)="onFieldValidationChange($event)">
      </app-checkbox-field>
    }

    <!-- File-based fields -->
    @case ('file') {
      <app-file-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)"
        (validationChange)="onFieldValidationChange($event)">
      </app-file-field>
    }

    <!-- User field -->
    @case ('user') {
      <app-user-field
        [config]="config"
        (valueChange)="onFieldValueChange($event)"
        (validationChange)="onFieldValidationChange($event)">
      </app-user-field>
    }

    <!-- Fallback for unknown field types -->
    @default {
      <div class="unknown-field-type">
        <div class="alert alert-warning">
          <strong>{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.UNKNOWN_FIELD_TYPE' | translate }}:</strong>
          {{ config.field.type }}
        </div>
      </div>
    }
  }

</div>
