<!-- Textarea Field Container -->
@if (shouldShowField()) {
  <div class="textarea-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display mock data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="field-type-icon me-2">notes</mat-icon>
          <div class="mock-value textarea-content">{{ mockValue() }}</div>
        </div>
      }
      
      <!-- FORM MODE: Display textarea control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <mat-icon matIconPrefix class="field-type-icon">notes</mat-icon>
          
          <textarea 
            matInput
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnly()"
            [class.read-only-cursor]="isReadOnly()"
            rows="4"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="8">
          </textarea>
          
          <!-- Character count -->
          <mat-hint align="end">
            {{ getCharacterCount() }}/{{ getMaxLength() }}
          </mat-hint>
          
          @if (formControl().invalid && formControl().touched) {
            <mat-error>
              @if (formControl().hasError('required')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED' | translate }}
              }
              @else if (formControl().hasError('minlength')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MIN_LENGTH' | translate: {min: formControl().getError('minlength').requiredLength} }}
              }
              @else if (formControl().hasError('maxlength')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_LENGTH' | translate: {max: formControl().getError('maxlength').requiredLength} }}
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
